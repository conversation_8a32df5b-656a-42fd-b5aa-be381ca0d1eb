#!/bin/bash

# 🚀 Flutter应用iPhone真机安装脚本
# 使用方法: ./install_iphone.sh

echo "📱 Flutter AI - iPhone真机安装脚本"
echo "=================================="

# 检查是否在Flutter项目目录
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 错误: 请在Flutter项目根目录运行此脚本"
    exit 1
fi

# 检查Flutter是否安装
if ! command -v flutter &> /dev/null; then
    echo "❌ 错误: Flutter未安装或不在PATH中"
    echo "请先安装Flutter: https://flutter.dev/docs/get-started/install"
    exit 1
fi

# 检查Xcode是否安装
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ 错误: Xcode未安装"
    echo "请从App Store安装Xcode"
    exit 1
fi

echo "✅ 环境检查通过"

# 清理项目
echo "🧹 清理项目..."
flutter clean

# 获取依赖
echo "📦 获取依赖..."
flutter pub get

# 检查连接的设备
echo "🔍 检查连接的设备..."
flutter devices

# 检查是否有iOS设备连接
if ! flutter devices | grep -q "ios"; then
    echo "⚠️  警告: 未检测到iOS设备"
    echo "请确保:"
    echo "1. iPhone已通过USB连接到Mac"
    echo "2. iPhone上已点击'信任此电脑'"
    echo "3. iPhone已解锁"
    echo ""
    read -p "设备已连接？按Enter继续，或Ctrl+C取消..."
fi

# 构建并安装到设备
echo "🚀 开始安装到iPhone..."
echo "注意: 首次安装可能需要几分钟时间"

# 尝试安装
if flutter run --release; then
    echo ""
    echo "🎉 安装成功！"
    echo ""
    echo "📋 接下来的步骤:"
    echo "1. 在iPhone主屏幕找到 'Flutter AI' 应用"
    echo "2. 如果应用无法打开，请到 设置 → 通用 → VPN与设备管理 → 信任开发者"
    echo "3. 打开应用测试通知功能"
    echo "4. 在 设置 → 通知 → Flutter AI 中确保通知已开启"
    echo ""
    echo "🔔 测试通知:"
    echo "- 点击应用中的'发送系统通知'按钮"
    echo "- 下拉iPhone顶部查看通知中心"
    echo "- 真机上的通知应该能正常显示"
else
    echo ""
    echo "❌ 安装失败"
    echo ""
    echo "🔧 可能的解决方案:"
    echo "1. 检查Xcode中的签名配置:"
    echo "   open ios/Runner.xcworkspace"
    echo "2. 在Xcode中选择你的Apple ID作为开发团队"
    echo "3. 修改Bundle Identifier为唯一值"
    echo "4. 确保iPhone设备已信任"
    echo ""
    echo "📖 详细说明请查看: install_to_iphone.md"
fi
