import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

void main() {
  group('通知功能测试', () {
    late FlutterLocalNotificationsPlugin plugin;

    setUp(() {
      plugin = FlutterLocalNotificationsPlugin();
    });

    testWidgets('通知插件初始化测试', (WidgetTester tester) async {
      // 模拟平台方法调用
      const MethodChannel channel = MethodChannel('dexterous.com/flutter/local_notifications');
      
      // 设置模拟方法调用处理器
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        if (methodCall.method == 'initialize') {
          return true;
        }
        return null;
      });

      // 测试初始化设置
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );
      
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // 验证初始化不会抛出异常
      expect(() async => await plugin.initialize(initializationSettings), 
             returnsNormally);
    });

    testWidgets('通知详情配置测试', (WidgetTester tester) async {
      // 测试Android通知配置
      const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'flutter_ai_channel',
        'Flutter AI 通知',
        channelDescription: 'Flutter AI 应用的通知频道',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: false,
      );

      expect(androidDetails.channelId, equals('flutter_ai_channel'));
      expect(androidDetails.channelName, equals('Flutter AI 通知'));
      expect(androidDetails.importance, equals(Importance.max));
      expect(androidDetails.priority, equals(Priority.high));
    });
  });
}
