# 📱 Flutter应用安装到iPhone真机指南

## 🔧 准备工作

### 1. 安装Xcode
```bash
# 从App Store安装Xcode（免费，但很大，约12GB）
# 或者运行以下命令检查是否已安装
xcode-select --version
```

### 2. 安装Xcode命令行工具
```bash
sudo xcode-select --install
```

### 3. 接受Xcode许可协议
```bash
sudo xcodebuild -license accept
```

## 📋 配置步骤

### 1. 连接iPhone
- 用USB数据线连接iPhone到Mac
- iPhone上会弹出"信任此电脑"，点击"信任"
- 输入iPhone密码确认

### 2. 检查设备连接
```bash
# 检查Flutter是否识别到设备
flutter devices

# 应该看到类似输出：
# iPhone (mobile) • ********-001234567890123A • ios • iOS 17.0
```

### 3. 配置iOS项目

#### 打开iOS项目配置
```bash
# 在项目根目录运行
open ios/Runner.xcworkspace
```

#### 在Xcode中配置：

1. **选择开发团队**：
   - 点击左侧 "Runner" 项目
   - 选择 "Signing & Capabilities" 标签
   - 在 "Team" 下拉菜单中选择你的Apple ID
   - 如果没有，点击 "Add Account" 添加你的Apple ID

2. **配置Bundle Identifier**：
   - 修改 "Bundle Identifier" 为唯一值
   - 例如：`com.yourname.flutter_ai`
   - 不能使用 `com.example.*`

3. **选择设备**：
   - 在Xcode顶部选择你的iPhone设备
   - 不要选择模拟器

## 🚀 安装应用

### 方法1：使用Flutter命令（推荐）
```bash
# 确保iPhone已连接并信任
flutter devices

# 安装到iPhone
flutter run --release

# 或者指定设备ID
flutter run -d "你的设备ID" --release
```

### 方法2：使用Xcode
1. 在Xcode中点击 "Play" 按钮（▶️）
2. 等待编译完成
3. 应用会自动安装到iPhone

## ⚠️ 可能遇到的问题

### 问题1：开发者证书问题
**错误信息**：`Signing for "Runner" requires a development team`

**解决方案**：
1. 在Xcode中添加Apple ID账号
2. 选择正确的开发团队
3. 确保Bundle ID唯一

### 问题2：设备未信任
**错误信息**：`Could not find any available provisioning profiles`

**解决方案**：
1. iPhone设置 → 通用 → VPN与设备管理
2. 找到你的开发者应用
3. 点击"信任"

### 问题3：免费账号限制
**错误信息**：`Maximum number of apps for free development profiles reached`

**解决方案**：
1. 删除其他测试应用
2. 或者购买开发者账号（$99/年）

### 问题4：iOS版本不兼容
**解决方案**：
```bash
# 检查iOS部署目标
# 编辑 ios/Runner.xcodeproj/project.pbxproj
# 找到 IPHONEOS_DEPLOYMENT_TARGET 并设置为合适版本
```

## 🔍 验证安装

### 1. 检查应用是否安装
- 在iPhone主屏幕查找应用图标
- 应用名称应该是 "flutter_ai"

### 2. 测试通知功能
1. 打开应用
2. 等待"✅ 通知功能已就绪"
3. 点击"发送系统通知"
4. 下拉通知中心查看通知

### 3. 检查权限
- iPhone设置 → 通知 → flutter_ai
- 确保"允许通知"已开启

## 📝 常用命令

```bash
# 查看连接的设备
flutter devices

# 清理项目
flutter clean

# 获取依赖
flutter pub get

# 运行在特定设备
flutter run -d "设备ID"

# 构建iOS应用
flutter build ios

# 查看日志
flutter logs
```

## 🎯 成功标志

如果看到以下信息，说明安装成功：
```
✓ Built ios/Runner.app
Installing and launching...
Syncing files to device iPhone...
Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h Repeat this help message.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).
```

## 💡 小贴士

1. **首次安装较慢** - 需要下载和编译依赖
2. **保持设备解锁** - 安装过程中不要锁屏
3. **网络稳定** - 确保网络连接良好
4. **存储空间** - 确保iPhone有足够存储空间
5. **iOS版本** - 建议iOS 12.0以上

安装成功后，你就可以在真机上测试通知功能了！
