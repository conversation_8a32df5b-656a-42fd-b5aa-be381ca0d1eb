import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest_all.dart' as tz_data;
import 'package:timezone/timezone.dart' as tz;

// iOS前台通知处理函数
void onDidReceiveLocalNotification(int id, String? title, String? body, String? payload) async {
  print('iOS前台通知: id=$id, title=$title, body=$body');
  // 这个回调在iOS 10以下版本使用，现在主要用于确保前台通知显示
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化时区数据（定时通知需要）
  try {
    tz_data.initializeTimeZones();
    tz.setLocalLocation(tz.getLocation('Asia/Shanghai')); // 设置为中国时区
    print('✅ 时区初始化成功');
  } catch (e) {
    print('⚠️ 时区初始化失败: $e，使用默认时区');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        // This is the theme of your application.
        //
        // TRY THIS: Try running your application with "flutter run". You'll see
        // the application has a purple toolbar. Then, without quitting the app,
        // try changing the seedColor in the colorScheme below to Colors.green
        // and then invoke "hot reload" (save your changes or press the "hot
        // reload" button in a Flutter-supported IDE, or press "r" if you used
        // the command line to start the app).
        //
        // Notice that the counter didn't reset back to zero; the application
        // state is not lost during the reload. To reset the state, use hot
        // restart instead.
        //
        // This works for code too, not just values: Most code changes can be
        // tested with just a hot reload.
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;
  FlutterLocalNotificationsPlugin? _notificationsPlugin;
  bool _isNotificationInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  // 完整的通知初始化
  Future<void> _initializeNotifications() async {
    _notificationsPlugin = FlutterLocalNotificationsPlugin();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // 增强的iOS设置 - 关键：允许前台显示通知
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
      requestCriticalPermission: true,
      // 🔑 关键设置：允许应用在前台时显示通知
      defaultPresentAlert: true,    // 显示横幅/弹窗
      defaultPresentSound: true,    // 播放声音
      defaultPresentBadge: true,    // 显示角标
      // 新增：确保前台通知显示
      onDidReceiveLocalNotification: onDidReceiveLocalNotification,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
      macOS: initializationSettingsIOS, // 添加macOS支持
    );

    try {
      // 等待初始化完成
      final bool? initialized = await _notificationsPlugin?.initialize(initializationSettings);
      print('通知插件初始化结果: $initialized');

      // 请求Android权限
      if (initialized == true) {
        await _notificationsPlugin?.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()?.requestNotificationsPermission();
      }

      // 检查iOS权限
      final iosPlugin = _notificationsPlugin?.resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin>();
      if (iosPlugin != null) {
        final bool? iosPermission = await iosPlugin.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
          critical: true,
        );
        print('iOS通知权限请求结果: $iosPermission');
      }

      setState(() {
        _isNotificationInitialized = initialized ?? false;
      });

      print('通知初始化${_isNotificationInitialized ? '成功' : '失败'}');
    } catch (e) {
      print('通知初始化错误: $e');
      setState(() {
        _isNotificationInitialized = false;
      });
    }
  }

  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }

  // 检查通知权限状态
  Future<void> _checkNotificationPermissions() async {
    if (_notificationsPlugin == null) return;

    try {
      // 检查iOS权限
      final iosPlugin = _notificationsPlugin?.resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin>();
      if (iosPlugin != null) {
        final bool? hasPermission = await iosPlugin.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
          critical: true,
        );
        print('iOS通知权限状态: $hasPermission');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('iOS通知权限: ${hasPermission == true ? "已授权" : "未授权或被拒绝"}'),
            backgroundColor: hasPermission == true ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );

        // 如果权限被拒绝，提示用户手动开启
        if (hasPermission != true) {
          _showPermissionDialog();
        }
      }
    } catch (e) {
      print('检查权限失败: $e');
    }
  }

  // 显示权限设置对话框
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('需要通知权限'),
          content: const Text(
            '要接收通知，请手动开启权限：\n\n'
            '1. 打开iPhone设置\n'
            '2. 找到"通知"\n'
            '3. 找到"Flutter AI"应用\n'
            '4. 开启"允许通知"\n'
            '5. 确保"横幅"和"通知中心"都已开启'
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('知道了'),
            ),
          ],
        );
      },
    );
  }

  // 测试后台通知
  void _testBackgroundNotification() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('🔍 iOS通知测试'),
          content: const Text(
            'iOS特性说明：\n\n'
            '📱 前台运行时：通知可能不显示在通知中心\n'
            '🔒 后台/锁屏时：通知会正常显示\n\n'
            '测试步骤：\n'
            '1. 点击"发送简单通知"\n'
            '2. 立即按Home键或锁屏\n'
            '3. 查看通知是否出现\n\n'
            '这是iOS的正常行为！'
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('明白了'),
            ),
          ],
        );
      },
    );
  }

  // 发送简单测试通知
  Future<void> _showSimpleNotification() async {
    if (_notificationsPlugin == null || !_isNotificationInitialized) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('通知功能未就绪')),
      );
      return;
    }

    try {
      print('发送简单通知...');

      // 最简单的通知配置
      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      );

      await _notificationsPlugin!.show(
        0,
        '测试通知',
        '这是一个简单的测试通知',
        platformChannelSpecifics,
      );

      print('✅ 简单通知发送完成');
    } catch (e) {
      print('❌ 简单通知发送失败: $e');
    }
  }

  // 定时通知 - 2秒后触发（真正的定时通知）
  Future<void> _showScheduledNotification() async {
    if (_notificationsPlugin == null || !_isNotificationInitialized) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('通知功能未就绪')),
      );
      return;
    }

    try {
      // 显示倒计时提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('⏰ 2秒后发送通知，请快速切换到后台！'),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.orange,
        ),
      );

      print('⏰ 设置2秒后的定时通知，请切换到后台...');

      // 计算2秒后的时间（使用timezone）
      final scheduledTime = tz.TZDateTime.now(tz.local).add(const Duration(seconds: 2));

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
        android: AndroidNotificationDetails(
          'scheduled_channel',
          '定时通知',
          channelDescription: '定时发送的通知',
          importance: Importance.max,
          priority: Priority.high,
        ),
      );

      // 使用系统级定时通知，不受应用生命周期影响
      await _notificationsPlugin!.zonedSchedule(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        '🎉 定时通知',
        '这是2秒后发送的定时通知！计数器值：$_counter',
        scheduledTime,
        platformChannelSpecifics,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      );

      print('✅ 定时通知已设置，将在 ${scheduledTime.toString()} 触发');

    } catch (e) {
      print('❌ 定时通知设置失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('定时通知设置失败: $e')),
      );
    }
  }

  // 发送系统通知栏通知
  Future<void> _showNotification() async {
    if (_notificationsPlugin == null || !_isNotificationInitialized) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('通知功能未就绪，请稍后再试')),
      );
      return;
    }

    try {
      print('开始发送通知...');

      // Android通知详细配置
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'flutter_ai_channel_id',
        'Flutter AI 通知',
        channelDescription: 'Flutter AI 应用的系统通知',
        importance: Importance.max,
        priority: Priority.high,
        ticker: 'Flutter AI 通知',
        showWhen: true,
        playSound: true,
        enableVibration: true,
        icon: '@mipmap/ic_launcher',
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
      );

      // 增强的iOS通知配置
      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        badgeNumber: 1,
        subtitle: 'Flutter AI 应用',
        threadIdentifier: 'flutter_ai_thread',
        // 添加附件和操作
        attachments: [],
        categoryIdentifier: 'flutter_ai_category',
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
        macOS: iOSPlatformChannelSpecifics,
      );

      final notificationId = DateTime.now().millisecondsSinceEpoch.remainder(100000);
      final title = 'Flutter AI 通知';
      final body = '你点击了通知按钮！当前计数器值：$_counter';

      print('通知ID: $notificationId');
      print('通知标题: $title');
      print('通知内容: $body');

      // 发送通知到系统通知栏
      await _notificationsPlugin!.show(
        notificationId,
        title,
        body,
        platformChannelSpecifics,
      );

      print('✅ 通知发送成功');

      // 显示应用内确认
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('✅ 通知已发送！'),
            ],
          ),
          duration: const Duration(seconds: 5),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } catch (e) {
      print('❌ 发送通知失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发送通知失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Column has various properties to control how it sizes itself and
          // how it positions its children. Here we use mainAxisAlignment to
          // center the children vertically; the main axis here is the vertical
          // axis because Columns are vertical (the cross axis would be
          // horizontal).
          //
          // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
          // action in the IDE, or press "p" in the console), to see the
          // wireframe for each widget.
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text('You have pushed the button this many times:'),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 40),
            // 通知状态指示
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _isNotificationInitialized ? Colors.green.shade100 : Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _isNotificationInitialized ? '✅ 通知功能已就绪' : '⏳ 通知功能初始化中...',
                style: TextStyle(
                  color: _isNotificationInitialized ? Colors.green.shade800 : Colors.orange.shade800,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 20),
            // 简单通知按钮
            ElevatedButton.icon(
              onPressed: _isNotificationInitialized ? _showSimpleNotification : null,
              icon: const Icon(Icons.notification_add),
              label: const Text('发送简单通知'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                textStyle: const TextStyle(fontSize: 14),
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 10),
            // 定时通知按钮 - 重点推荐
            ElevatedButton.icon(
              onPressed: _isNotificationInitialized ? _showScheduledNotification : null,
              icon: const Icon(Icons.schedule),
              label: const Text('⏰ 2秒后定时通知'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 5),
            Text(
              '👆 推荐：点击后快速切换到后台',
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange.shade700,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            // 完整通知按钮
            ElevatedButton.icon(
              onPressed: _isNotificationInitialized ? _showNotification : null,
              icon: _isNotificationInitialized
                  ? const Icon(Icons.notifications_active)
                  : const Icon(Icons.notifications_off),
              label: Text(_isNotificationInitialized ? '发送完整通知' : '请等待初始化...'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                textStyle: const TextStyle(fontSize: 16),
                backgroundColor: _isNotificationInitialized ? null : Colors.grey,
              ),
            ),
            const SizedBox(height: 10),
            // 权限检查按钮
            TextButton.icon(
              onPressed: _checkNotificationPermissions,
              icon: const Icon(Icons.security, size: 16),
              label: const Text('检查通知权限'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue.shade600,
              ),
            ),
            const SizedBox(height: 5),
            // 后台测试按钮
            TextButton.icon(
              onPressed: _testBackgroundNotification,
              icon: const Icon(Icons.phone_android, size: 16),
              label: const Text('测试后台通知'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.orange.shade600,
              ),
            ),
            const SizedBox(height: 10),
            // iOS模拟器专用说明
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                border: Border.all(color: Colors.blue.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue.shade600, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'iOS模拟器通知测试',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
