import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        // This is the theme of your application.
        //
        // TRY THIS: Try running your application with "flutter run". You'll see
        // the application has a purple toolbar. Then, without quitting the app,
        // try changing the seedColor in the colorScheme below to Colors.green
        // and then invoke "hot reload" (save your changes or press the "hot
        // reload" button in a Flutter-supported IDE, or press "r" if you used
        // the command line to start the app).
        //
        // Notice that the counter didn't reset back to zero; the application
        // state is not lost during the reload. To reset the state, use hot
        // restart instead.
        //
        // This works for code too, not just values: Most code changes can be
        // tested with just a hot reload.
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;
  FlutterLocalNotificationsPlugin? _notificationsPlugin;
  bool _isNotificationInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  // 完整的通知初始化
  Future<void> _initializeNotifications() async {
    _notificationsPlugin = FlutterLocalNotificationsPlugin();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    try {
      // 等待初始化完成
      final bool? initialized = await _notificationsPlugin?.initialize(initializationSettings);

      // 请求Android 13+的通知权限
      if (initialized == true) {
        await _notificationsPlugin?.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()?.requestNotificationsPermission();
      }

      setState(() {
        _isNotificationInitialized = initialized ?? false;
      });

      print('通知初始化${_isNotificationInitialized ? '成功' : '失败'}');
    } catch (e) {
      print('通知初始化错误: $e');
      setState(() {
        _isNotificationInitialized = false;
      });
    }
  }

  void _incrementCounter() {
    setState(() {
      // This call to setState tells the Flutter framework that something has
      // changed in this State, which causes it to rerun the build method below
      // so that the display can reflect the updated values. If we changed
      // _counter without calling setState(), then the build method would not be
      // called again, and so nothing would appear to happen.
      _counter++;
    });
  }

  // 发送系统通知栏通知
  Future<void> _showNotification() async {
    if (_notificationsPlugin == null || !_isNotificationInitialized) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('通知功能未就绪，请稍后再试')),
      );
      return;
    }

    try {
      // Android通知详细配置
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'flutter_ai_channel_id',
        'Flutter AI 通知',
        channelDescription: 'Flutter AI 应用的系统通知',
        importance: Importance.max,
        priority: Priority.high,
        ticker: 'Flutter AI 通知',
        // 确保显示在通知栏
        showWhen: true,
        when: null,
        // 添加声音和震动
        playSound: true,
        enableVibration: true,
        // 设置通知图标
        icon: '@mipmap/ic_launcher',
        // 设置大图标
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
      );

      // iOS通知配置
      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // 发送通知到系统通知栏
      await _notificationsPlugin!.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000), // 使用时间戳作为ID
        'Flutter AI 通知', // 通知标题
        '你点击了通知按钮！当前计数器值：$_counter', // 通知内容
        platformChannelSpecifics,
      );

      print('系统通知已发送');

      // 显示应用内确认
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('系统通知已发送！请查看通知栏'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      print('发送通知失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发送通知失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Column has various properties to control how it sizes itself and
          // how it positions its children. Here we use mainAxisAlignment to
          // center the children vertically; the main axis here is the vertical
          // axis because Columns are vertical (the cross axis would be
          // horizontal).
          //
          // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
          // action in the IDE, or press "p" in the console), to see the
          // wireframe for each widget.
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text('You have pushed the button this many times:'),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 40),
            // 通知状态指示
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _isNotificationInitialized ? Colors.green.shade100 : Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _isNotificationInitialized ? '✅ 通知功能已就绪' : '⏳ 通知功能初始化中...',
                style: TextStyle(
                  color: _isNotificationInitialized ? Colors.green.shade800 : Colors.orange.shade800,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: _isNotificationInitialized ? _showNotification : null,
              icon: _isNotificationInitialized
                  ? const Icon(Icons.notifications_active)
                  : const Icon(Icons.notifications_off),
              label: Text(_isNotificationInitialized ? '发送系统通知' : '请等待初始化...'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                textStyle: const TextStyle(fontSize: 16),
                backgroundColor: _isNotificationInitialized ? null : Colors.grey,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              '点击按钮后，请下拉手机顶部查看通知栏',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
